<?php
namespace App\Livewire;

use Livewire\Component;
use App\Models\User;
use App\Models\UserMatch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class SwipeMatch extends Component
{
    public $users;
    public $index = 0;
    public $maxDistance = 50; // Default max distance in km
    public $showLocationError = false;
    public $locationErrorMessage = '';
    public $likedUsers = []; // Lista de usuários curtidos

    public function mount()
    {
        $this->users = collect([]);
        $this->users = $this->loadCandidates();
        $this->loadLikedUsers();
    }







    /**
     * Carrega a lista de usuários que o usuário atual curtiu
     */
    protected function loadLikedUsers()
    {
        $likedMatches = UserMatch::where('user_id', Auth::id())
            ->where('liked', true)
            ->with('targetUser')
            ->get();

        $this->likedUsers = $likedMatches->map(function($match) {
            $user = $match->targetUser;
            if (!$user || !isset($user->id)) {
                return null;
            }
            return [
                'user' => $user,
                'hasMatched' => $match->is_matched,
                'matchedAt' => $match->matched_at
            ];
        })->filter()->toArray(); // Remove valores null
    }

    public function updatedMaxDistance()
    {
        // Reload candidates when max distance changes
        $this->users = $this->loadCandidates();
        $this->index = 0; // Reset index to start from the beginning
    }



    /**
     * Carrega os candidatos para o radar
     *
     * @return \Illuminate\Support\Collection
     */
    public function loadCandidates()
    {
        $current = Auth::user();

        // Verifica se o usuário tem as coordenadas
        if (!$current->latitude || !$current->longitude) {
            $this->showLocationError = true;
            $this->locationErrorMessage = 'Sua localização não está disponível. Permita o acesso à sua localização no navegador ou atualize manualmente nas configurações.';
            return collect([]);
        }

        // Carrega usuários que não sejam o atual, e que não tenham sido "passados" ou "curtidos"
        $users = User::where('id', '!=', $current->id)
            ->whereNotIn('id', function ($q) use ($current) {
                $q->select('target_user_id')
                    ->from('user_matches')
                    ->where('user_id', $current->id); // Exclui todos os usuários que já receberam interação (pass ou like)
            })
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->with(['photos', 'currentPhoto', 'city']) // Carrega as fotos, foto atual e cidade dos usuários
            ->get();

        // Filtra e adiciona a distância a cada usuário
        $filteredUsers = $users->map(function ($user) use ($current) {
                // Verifica se os valores de latitude e longitude são válidos
                if (
                    empty($current->latitude) || empty($current->longitude) ||
                    empty($user->latitude) || empty($user->longitude) ||
                    !is_numeric($current->latitude) || !is_numeric($current->longitude) ||
                    !is_numeric($user->latitude) || !is_numeric($user->longitude)
                ) {
                    // Atribui uma distância grande para usuários sem coordenadas válidas
                    $user->distance = 999999;
                    return $user;
                }

                // Calcula a distância de cada usuário
                $distance = $this->calculateDistance(
                    $current->latitude, $current->longitude,
                    $user->latitude, $user->longitude
                );

                // Adiciona a distância ao objeto do usuário
                $user->distance = $distance;

                return $user;
            })
            ->filter(function ($user) {
                // Filtra usuários pela distância máxima definida e com distância válida
                return $user->distance <= $this->maxDistance && $user->distance < 999999;
            })
            ->sortBy('distance') // Ordena por distância (mais próximos primeiro)
            ->values();

        if ($filteredUsers->isEmpty()) {
            if ($users->isEmpty()) {
                $this->showLocationError = true;
                $this->locationErrorMessage = 'Não encontramos outros usuários com localização definida no sistema.';
            } else {
                $this->showLocationError = true;
                $this->locationErrorMessage = 'Não encontramos usuários próximos dentro de ' . $this->maxDistance . 'km. Tente aumentar a distância.';
            }
        } else {
            $this->showLocationError = false;
        }

        return $filteredUsers;
    }

    public function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        // Garantir que todos os valores são numéricos
        $lat1 = (float) $lat1;
        $lng1 = (float) $lng1;
        $lat2 = (float) $lat2;
        $lng2 = (float) $lng2;

        // Verificar se algum valor é inválido
        if (!is_numeric($lat1) || !is_numeric($lng1) || !is_numeric($lat2) || !is_numeric($lng2)) {
            return 999999; // Retorna um valor grande para indicar distância inválida
        }

        $earthRadius = 6371; // Raio da Terra em quilômetros

        // Converter as coordenadas de graus para radianos
        $lat1 = deg2rad($lat1);
        $lng1 = deg2rad($lng1);
        $lat2 = deg2rad($lat2);
        $lng2 = deg2rad($lng2);

        // Diferenças
        $dLat = $lat2 - $lat1;
        $dLng = $lng2 - $lng1;

        // Fórmula de Haversine
        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos($lat1) * cos($lat2) *
            sin($dLng / 2) * sin($dLng / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earthRadius * $c; // Distância em quilômetros

        return $distance;
    }

    public function like($userId = null)
    {
        // Determina qual usuário será curtido
        $targetUserId = $userId;
        if ($userId === null && $this->users->count() > 0 && $this->index >= 0) {
            $currentUser = $this->users->get($this->index);
            if ($currentUser && isset($currentUser->id)) {
                $targetUserId = $currentUser->id;
            }
        }

        if (!$targetUserId) {
            return; // Não há usuário para curtir
        }

        // Armazena o match
        $this->storeMatch(true, $targetUserId);

        // Recarrega a lista de usuários curtidos
        $this->loadLikedUsers();

        // Avança para o próximo usuário automaticamente
        $this->nextUser();

        // Força a atualização da interface
        $this->dispatch('userLiked', ['userId' => $targetUserId]);
    }

    public function pass($userId = null)
    {
        // Determina qual usuário será passado
        $targetUserId = $userId;
        if ($userId === null && $this->users->count() > 0 && $this->index >= 0) {
            $currentUser = $this->users->get($this->index);
            if ($currentUser && isset($currentUser->id)) {
                $targetUserId = $currentUser->id;
            }
        }

        if (!$targetUserId) {
            return; // Não há usuário para passar
        }

        // Armazena o match
        $this->storeMatch(false, $targetUserId);

        // Avança para o próximo usuário automaticamente
        $this->nextUser();

        // Força a atualização da interface
        $this->dispatch('userPassed', ['userId' => $targetUserId]);
    }

    public function storeMatch($liked, $targetUserId)
    {
        $me = Auth::user();

        if (!$targetUserId) {
            return;
        }

        // Busca o usuário alvo
        $target = User::find($targetUserId);
        if (!$target) {
            return;
        }

        // Verifica se já existe um match anterior
        $existingMatch = UserMatch::where('user_id', $me->id)
            ->where('target_user_id', $targetUserId)
            ->first();

        if ($existingMatch) {
            // Atualiza o match existente
            $existingMatch->update([
                'liked' => $liked
            ]);
            $userMatch = $existingMatch;
        } else {
            // Cria um novo match
            $userMatch = UserMatch::create([
                'user_id' => $me->id,
                'target_user_id' => $targetUserId,
                'liked' => $liked
            ]);
        }

        // Verifica se houve match recíproco (apenas se o usuário deu like)
        if ($liked) {
            $reciprocal = UserMatch::where('user_id', $targetUserId)
                ->where('target_user_id', $me->id)
                ->where('liked', true)
                ->first();

            if ($reciprocal) {
                // Atualiza ambos os registros para indicar que houve match
                $userMatch->update([
                    'is_matched' => true,
                    'matched_at' => now()
                ]);

                $reciprocal->update([
                    'is_matched' => true,
                    'matched_at' => now()
                ]);

                // Notifica o usuário sobre o match
                $this->showMatchNotification($target);
            }
        } else {
            // Se o usuário deu "pass", verifica se havia um match anterior e remove
            $reciprocal = UserMatch::where('user_id', $targetUserId)
                ->where('target_user_id', $me->id)
                ->where('is_matched', true)
                ->first();

            if ($reciprocal) {
                // Remove o status de match
                $userMatch->update([
                    'is_matched' => false,
                    'matched_at' => null
                ]);

                $reciprocal->update([
                    'is_matched' => false,
                    'matched_at' => null
                ]);
            }
        }
    }

    /**
     * Exibe a notificação de match
     */
    protected function showMatchNotification($matchedUser)
    {
        if (!$matchedUser || !isset($matchedUser->name)) {
            return;
        }

        // Exibe a notificação de match
        session()->flash('match', [
            'user' => $matchedUser,
            'message' => "🎉 Você deu match com {$matchedUser->name}!"
        ]);

        // Aqui você pode adicionar lógica para enviar notificações push, emails, etc.
    }

    /**
     * Verifica se o usuário atual já deu like em um usuário específico
     */
    public function hasLiked($userId)
    {
        if (!$userId) {
            return false;
        }

        return UserMatch::where('user_id', Auth::id())
            ->where('target_user_id', $userId)
            ->where('liked', true)
            ->exists();
    }

    /**
     * Verifica se o usuário atual já deu pass em um usuário específico
     */
    public function hasPassed($userId)
    {
        if (!$userId) {
            return false;
        }

        return UserMatch::where('user_id', Auth::id())
            ->where('target_user_id', $userId)
            ->where('liked', false)
            ->exists();
    }

    /**
     * Verifica se o usuário atual já deu match com um usuário específico
     */
    public function hasMatched($userId)
    {
        if (!$userId) {
            return false;
        }

        return UserMatch::where('user_id', Auth::id())
            ->where('target_user_id', $userId)
            ->where('is_matched', true)
            ->exists();
    }

    /**
     * Avança para o próximo usuário manualmente
     */
    public function nextUser()
    {
        // Remove o usuário atual da lista se existir
        if ($this->users->count() > 0 && $this->index >= 0 && $this->index < $this->users->count()) {
            $this->users = $this->users->forget($this->index)->values();
        }

        // Ajusta o índice se necessário
        if ($this->index >= $this->users->count()) {
            $this->index = max(0, $this->users->count() - 1);
        }

        // Se não houver mais usuários, recarrega os candidatos
        if ($this->users->count() === 0) {
            $this->users = $this->loadCandidates();
            $this->index = 0;
        }

        // Força a atualização da interface
        $this->dispatch('userChanged');
    }

    /**
     * Recarrega todos os candidatos manualmente
     */
    public function reloadCandidates()
    {
        $this->users = $this->loadCandidates();
        $this->index = 0;
        $this->loadLikedUsers();
    }

    public function render()
    {
        // Garante que $this->users seja uma Collection
        if (!$this->users instanceof \Illuminate\Support\Collection) {
            $this->users = collect($this->users ?? []);
        }

        // Garante que o índice não ultrapasse o tamanho da lista
        if ($this->index >= $this->users->count()) {
            $this->index = max(0, $this->users->count() - 1);
        }

        // Determina o usuário atual
        $currentUser = null;
        if ($this->users->count() > 0 && $this->index >= 0) {
            $currentUser = $this->users->get($this->index);
        }

        // Prepara dados para a view
        $userData = [];
        if ($currentUser && isset($currentUser->id)) {
            $userData = [
                'user' => $currentUser,
                'hasLiked' => $this->hasLiked($currentUser->id),
                'hasPassed' => $this->hasPassed($currentUser->id),
                'hasMatched' => $this->hasMatched($currentUser->id)
            ];
        }

        // Prepara dados para a lista de usuários próximos
        // Excluímos o usuário atual da lista de usuários próximos para evitar duplicação
        $nearbyUsers = $this->users
            ->filter(function ($user) use ($currentUser) {
                if (!$user || !isset($user->id)) {
                    return false;
                }
                return $currentUser && isset($currentUser->id) ? $user->id !== $currentUser->id : true;
            })
            ->map(function ($user) {
                if (!$user || !isset($user->id)) {
                    return null;
                }
                return [
                    'user' => $user,
                    'hasLiked' => $this->hasLiked($user->id),
                    'hasPassed' => $this->hasPassed($user->id),
                    'hasMatched' => $this->hasMatched($user->id)
                ];
            })
            ->filter(); // Remove valores null

        return view('livewire.swipe-match', [
            'currentUser' => $userData,
            'nearbyUsers' => $nearbyUsers,
            'showLocationError' => $this->showLocationError,
            'locationErrorMessage' => $this->locationErrorMessage,
            'maxDistance' => $this->maxDistance,
            'likedUsers' => $this->likedUsers
        ]);
    }
}
